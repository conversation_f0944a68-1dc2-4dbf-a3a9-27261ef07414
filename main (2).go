package main

import (
	"bufio"
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"os"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/charmbracelet/bubbles/list"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

var (
	titleSty = lipgloss.NewStyle().Bold(true).Foreground(lipgloss.Color("#ff00ff")).MarginLeft(2)
	docSty   = lipgloss.NewStyle().Margin(1, 2)
	dimSty   = lipgloss.NewStyle().Foreground(lipgloss.Color("#444444"))
	emailRe  = regexp.MustCompile(`^[^@\s]+@[^@\s]+\.[^@\s]+$`)
	ageRe    = regexp.MustCompile(`(\d+)\s*years\s*old`)
)

type cfg struct {
	threads   int
	batchSize int
	proxy     string
}

type stats struct {
	done        int64
	found       int64
	retries     int64
	start       time.Time
	totalEmails int
	hitsChan    chan string
}

func (s *stats) reset(totalEmails int) {
	atomic.StoreInt64(&s.done, 0)
	atomic.StoreInt64(&s.found, 0)
	atomic.StoreInt64(&s.retries, 0)
	s.start = time.Now()
	s.totalEmails = totalEmails
	for len(s.hitsChan) > 0 {
		<-s.hitsChan
	}
}

type personInfo struct {
	email   string
	name    string
	age     string
	address string
	phone   string
	wealth  string
}

type emailJob struct {
	email   string
	retries int
}

type simpleQueue struct {
	jobs      chan *emailJob
	totalJobs int
	processed int64
	ctx       context.Context
}

func NewSimpleQueue(ctx context.Context, emails []string) *simpleQueue {
	q := &simpleQueue{
		jobs:      make(chan *emailJob, len(emails)*3),
		totalJobs: len(emails),
		processed: 0,
		ctx:       ctx,
	}
	for _, email := range emails {
		q.jobs <- &emailJob{email: email, retries: 0}
	}
	return q
}

func (q *simpleQueue) getJob() (*emailJob, bool) {
	select {
	case job, ok := <-q.jobs:
		if !ok {
			return nil, false
		}
		return job, true
	case <-q.ctx.Done():
		return nil, false
	default:
		return nil, false
	}
}

func (q *simpleQueue) requeue(job *emailJob) {
	job.retries++
	select {
	case q.jobs <- job:
	case <-q.ctx.Done():
	default:
		atomic.AddInt64(&q.processed, 1)
	}
}

func (q *simpleQueue) markDone() {
	atomic.AddInt64(&q.processed, 1)
}

func (q *simpleQueue) isDone() bool {
	return atomic.LoadInt64(&q.processed) >= int64(q.totalJobs) && len(q.jobs) == 0
}

type item struct {
	title, desc string
	itemType    string
	value       *int
}

func (i item) FilterValue() string { return i.title }
func (i item) Title() string       { return i.title }
func (i item) Description() string { return i.desc }

type mdl struct {
	list       list.Model
	config     *cfg
	emails     []string
	stats      *stats
	choice     string
	quitting   bool
	editing    bool
	editValue  string
	processing bool
	completed  bool
	statusMsg  string
	ctx        context.Context
	cancel     context.CancelFunc
	recentHits []string
	hitsMutex  *sync.RWMutex
}

type doneMsg struct {
	processed int
	found     int
	cpm       float64
}

type quitMsg struct{}
type tickMsg time.Time

func tickCmd() tea.Cmd {
	return tea.Tick(time.Second, func(t time.Time) tea.Msg {
		return tickMsg(t)
	})
}

func initMdl() mdl {
	emails := loadEmails()
	if len(emails) == 0 {
		return mdl{quitting: true, statusMsg: "No emails found in emails.txt"}
	}
	config := &cfg{
		threads:   200,
		batchSize: 30,
		proxy:     "monkenegro1-zone-resi-region-de:approveorbitch1:aca5a67c9f3b0dad.wdc.eu.pyproxy.io:16666",
	}
	items := []list.Item{
		item{title: "Threads", desc: fmt.Sprintf("Current: %d", config.threads), itemType: "setting", value: &config.threads},
		item{title: "Batch Size", desc: fmt.Sprintf("Current: %d", config.batchSize), itemType: "setting", value: &config.batchSize},
		item{title: "Modules", desc: "Additional tools (coming soon)", itemType: "action"},
		item{title: "Start Processing", desc: fmt.Sprintf("Process %d emails", len(emails)), itemType: "action"},
		item{title: "Exit", desc: "Quit the application", itemType: "action"},
	}
	const defaultWidth = 20
	const listHeight = 20
	l := list.New(items, list.NewDefaultDelegate(), defaultWidth, listHeight)
	l.Title = "That's Them Auto-Doxxer"
	l.SetShowStatusBar(false)
	l.SetFilteringEnabled(false)
	l.SetShowPagination(false)
	l.Styles.Title = titleSty
	return mdl{
		list:      l,
		config:    config,
		emails:    emails,
		stats:     &stats{start: time.Now(), totalEmails: len(emails), hitsChan: make(chan string, 100)},
		hitsMutex: &sync.RWMutex{},
	}
}

func (m mdl) Init() tea.Cmd {
	return nil
}

func (m mdl) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.list.SetWidth(msg.Width)
		return m, nil
	case tea.KeyMsg:
		if m.quitting {
			return m, tea.Quit
		}
		if m.completed {
			switch keypress := msg.String(); keypress {
			case " ", "enter":
				m.completed = false
				m.statusMsg = ""
				return m, tea.ClearScreen
			case "ctrl+c", "q":
				return m, tea.Quit
			}
			return m, nil
		}
		if m.processing {
			switch keypress := msg.String(); keypress {
			case "ctrl+c":
				return m, func() tea.Msg { return quitMsg{} }
			case "enter":
				return m, tea.ClearScreen
			}
			return m, nil
		}
		if m.editing {
			switch keypress := msg.String(); keypress {
			case "enter":
				if value, err := strconv.Atoi(m.editValue); err == nil && value > 0 {
					selectedItem := m.list.SelectedItem().(item)
					*selectedItem.value = value
					items := make([]list.Item, len(m.list.Items()))
					for i, listItem := range m.list.Items() {
						if listItem == selectedItem {
							updatedItem := listItem.(item)
							updatedItem.desc = fmt.Sprintf("Current: %d", value)
							items[i] = updatedItem
						} else {
							items[i] = listItem
						}
					}
					m.list.SetItems(items)
				}
				m.editing = false
				m.editValue = ""
				return m, nil
			case "esc":
				m.editing = false
				m.editValue = ""
				return m, nil
			case "backspace":
				if len(m.editValue) > 0 {
					m.editValue = m.editValue[:len(m.editValue)-1]
				}
				return m, nil
			default:
				if len(msg.String()) == 1 && msg.String() >= "0" && msg.String() <= "9" {
					m.editValue += msg.String()
				}
				return m, nil
			}
		}
		switch keypress := msg.String(); keypress {
		case "ctrl+c":
			return m, tea.Quit
		case "q":
			m.quitting = true
			return m, tea.Quit
		case "enter":
			i, ok := m.list.SelectedItem().(item)
			if ok {
				if i.itemType == "setting" {
					m.editing = true
					m.editValue = strconv.Itoa(*i.value)
					m.choice = i.title
				} else if i.title == "Start Processing" {
					m.processing = true
					m.ctx, m.cancel = context.WithCancel(context.Background())
					m.hitsMutex.Lock()
					m.recentHits = []string{}
					m.hitsMutex.Unlock()
					return m, tea.Batch(
						tea.ClearScreen,
						tickCmd(),
						func() tea.Msg {
							processed, found, cpm := processEmails(m.ctx, m.emails, m.config, m.stats)
							return doneMsg{processed, found, cpm}
						},
					)
				} else if i.title == "Modules" {
					m.statusMsg = "Modules feature coming soon! Additional tools will be available here."
				} else if i.title == "Exit" {
					m.quitting = true
					return m, tea.Quit
				}
			}
			return m, nil
		}
	case quitMsg:
		if m.cancel != nil {
			m.cancel()
		}
		m.processing = false
		m.completed = false
		m.statusMsg = "Processing stopped by user"
		return m, tea.ClearScreen
	case tickMsg:
		if m.processing {
			for {
				select {
				case hit := <-m.stats.hitsChan:
					m.hitsMutex.Lock()
					m.recentHits = append(m.recentHits, hit)
					if len(m.recentHits) > 50 {
						m.recentHits = m.recentHits[len(m.recentHits)-50:]
					}
					m.hitsMutex.Unlock()
				default:
					goto tickDone
				}
			}
		tickDone:
			return m, tickCmd()
		}
		return m, nil
	case doneMsg:
		m.processing = false
		m.completed = true
		m.statusMsg = fmt.Sprintf("✓ Finished! Processed: %d | Found: %d | CPM: %.1f\n\nPress Space or Enter to return to main menu",
			msg.processed, msg.found, msg.cpm)
		return m, tea.ClearScreen
	}
	var cmd tea.Cmd
	m.list, cmd = m.list.Update(msg)
	return m, cmd
}

func (m mdl) View() string {
	if m.quitting && m.statusMsg != "" {
		return docSty.Render("✗ " + m.statusMsg)
	}
	if m.completed {
		s := titleSty.Render("Processing Complete!")
		s += "\n\n" + m.statusMsg
		return docSty.Render(s)
	}
	if m.processing {
		done := atomic.LoadInt64(&m.stats.done)
		found := atomic.LoadInt64(&m.stats.found)
		retries := atomic.LoadInt64(&m.stats.retries)
		elapsed := time.Since(m.stats.start)
		cpm := 0.0
		if elapsed.Seconds() >= 1 && done > 0 {
			cpm = float64(done) / elapsed.Minutes()
		}
		progress := float64(done) / float64(m.stats.totalEmails) * 100
		s := titleSty.Render("Processing emails...")
		s += fmt.Sprintf("\n\nProgress: %d/%d (%.1f%%) | Found: %d | Retries: %d | CPM: %.1f",
			done, m.stats.totalEmails, progress, found, retries, cpm)
		s += "\n\nPress Ctrl+C to stop and return to menu\n"
		s += strings.Repeat("─", 60) + "\n"
		m.hitsMutex.RLock()
		if len(m.recentHits) > 0 {
			startIdx := 0
			if len(m.recentHits) > 10 {
				startIdx = len(m.recentHits) - 10
			}
			for i := startIdx; i < len(m.recentHits); i++ {
				s += m.recentHits[i] + "\n"
			}
		} else {
			s += lipgloss.NewStyle().Foreground(lipgloss.Color("#666666")).Render("Waiting for results...") + "\n"
		}
		m.hitsMutex.RUnlock()
		return docSty.Render(s)
	}
	if m.editing {
		s := titleSty.Render(fmt.Sprintf("Edit %s", m.choice))
		s += "\n\nEnter new value: " + m.editValue
		if m.choice == "Batch Size" {
			s += "\n\n" + dimSty.Render("• Recommended: 30 (optimal captcha usage)")
			s += "\n" + dimSty.Render("• Less than 30: Higher captcha costs")
			s += "\n" + dimSty.Render("• Above 30: Untested, may cause issues")
		}
		s += "\n\nPress Enter to save, Esc to cancel"
		return docSty.Render(s)
	}
	s := m.list.View()
	if m.statusMsg != "" {
		s += "\n\n" + m.statusMsg
	}
	footer := "\nLoaded " + strconv.Itoa(len(m.emails)) + " emails • Press 'q' to quit"
	s += lipgloss.NewStyle().Foreground(lipgloss.Color("#666666")).Render(footer)
	return docSty.Render(s)
}

func main() {
	p := tea.NewProgram(initMdl(), tea.WithAltScreen())
	if _, err := p.Run(); err != nil {
		fmt.Printf("Error: %v", err)
		os.Exit(1)
	}
}

func loadEmails() []string {
	file, err := os.Open("emails.txt")
	if err != nil {
		return nil
	}
	defer file.Close()
	var emails []string
	emailSet := make(map[string]bool)
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		email := strings.TrimSpace(scanner.Text())
		if email == "" {
			continue
		}
		if strings.Contains(email, "+") && strings.Contains(email, "@") {
			parts := strings.Split(email, "@")
			if len(parts) == 2 {
				localPart := parts[0]
				domain := parts[1]
				if strings.Contains(localPart, "+") {
					baseEmail := strings.Split(localPart, "+")[0] + "@" + domain
					email = baseEmail
				}
			}
		}
		if emailRe.MatchString(email) {
			if !emailSet[email] {
				emailSet[email] = true
				emails = append(emails, email)
			}
		}
	}
	return emails
}

func processEmails(ctx context.Context, emails []string, config *cfg, stats *stats) (int, int, float64) {
	ensureCSV()
	stats.reset(len(emails))
	queue := NewSimpleQueue(ctx, emails)
	var wg sync.WaitGroup
	for i := 0; i < config.threads; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			worker(ctx, queue, config, stats)
		}()
	}
	wg.Wait()
	elapsed := time.Since(stats.start)
	processed := int(atomic.LoadInt64(&stats.done))
	found := int(atomic.LoadInt64(&stats.found))
	cpm := 0.0
	if elapsed.Minutes() > 0 {
		cpm = float64(processed) / elapsed.Minutes()
	}
	return processed, found, cpm
}

func worker(ctx context.Context, queue *simpleQueue, config *cfg, stats *stats) {
	for {
		select {
		case <-ctx.Done():
			return
		default:
		}
		captcha := solveCaptcha()
		if captcha == "" {
			continue
		}
		jar, _ := cookiejar.New(nil)
		client := &http.Client{
			Jar:     jar,
			Timeout: 8 * time.Second,
		}
		if config.proxy != "" {
			parts := strings.Split(config.proxy, ":")
			if len(parts) == 4 {
				proxyURL, _ := url.Parse(fmt.Sprintf("http://%s:%s@%s:%s", parts[0], parts[1], parts[2], parts[3]))
				client.Transport = &http.Transport{Proxy: http.ProxyURL(proxyURL)}
			}
		}
		challengeSuccess := submitCaptcha(client, captcha)
		if !challengeSuccess {
			continue
		}
		for i := 0; i < config.batchSize; i++ {
			select {
			case <-ctx.Done():
				return
			default:
			}
			job, ok := queue.getJob()
			if !ok {
				if queue.isDone() {
					return
				}
				break
			}
			success := processEmail(client, job.email, stats)
			if success {
				queue.markDone()
			} else {
				queue.requeue(job)
				atomic.AddInt64(&stats.retries, 1)
			}
		}
	}
}

func submitCaptcha(client *http.Client, captcha string) bool {
	data := url.Values{"g-recaptcha-response": {captcha}}
	req, err := http.NewRequest("POST", "https://thatsthem.com/challenge", strings.NewReader(data.Encode()))
	if err != nil {
		return false
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36")
	req.Header.Set("Referer", "https://thatsthem.com/")
	req.Header.Set("Connection", "close")
	resp, err := client.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	return resp.StatusCode == 200
}

func processEmail(client *http.Client, email string, stats *stats) bool {
	emailURL := fmt.Sprintf("https://thatsthem.com/email/%s", email)
	req, err := http.NewRequest("GET", emailURL, nil)
	if err != nil {
		return false
	}
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36")
	req.Header.Set("Referer", "https://thatsthem.com/")
	req.Header.Set("Connection", "close")
	resp, err := client.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		return false
	}
	atomic.AddInt64(&stats.done, 1)
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return true
	}
	person := extractInfo(doc, email)
	if person != nil {
		exportToCSV(person, email)
		logHit(email, person.name, stats)
		atomic.AddInt64(&stats.found, 1)
	}
	return true
}

func extractInfo(doc *goquery.Document, email string) *personInfo {
	if doc.Find("div.query:contains('Found 0 results')").Length() > 0 {
		return nil
	}
	record := doc.Find("div.record#r0").First()
	if record.Length() == 0 {
		record = doc.Find("div.records div.record").First()
	}
	if record.Length() == 0 {
		record = doc.Find("div.card").First()
	}
	if record.Length() == 0 {
		record = doc.Find("div[class*=record]").First()
	}
	if record.Length() == 0 {
		record = doc.Find("div.name, div.age, div.location, div.phone").First().Parent()
	}
	if record.Length() == 0 {
		return nil
	}
	person := &personInfo{email: email}
	nameDiv := record.Find("div.name").First()
	if nameDiv.Length() > 0 {
		nameLink := nameDiv.Find("a").First()
		if nameLink.Length() > 0 {
			person.name = strings.TrimSpace(nameLink.Text())
		} else {
			nameText := strings.TrimSpace(nameDiv.Text())
			if nameText != "" && !strings.Contains(nameText, "@") {
				person.name = nameText
			}
		}
	}
	ageDiv := record.Find("div.age").First()
	if ageDiv.Length() > 0 {
		ageText := ageDiv.Text()
		if matches := ageRe.FindStringSubmatch(ageText); len(matches) > 1 {
			person.age = matches[1]
		}
	}
	if person.age == "" {
		doc.Find("*").Each(func(i int, s *goquery.Selection) {
			text := s.Text()
			if matches := ageRe.FindStringSubmatch(text); len(matches) > 1 {
				person.age = matches[1]
				return
			}
		})
	}
	var address string
	record.Find("div.subtitle").Each(func(i int, subtitle *goquery.Selection) {
		if strings.Contains(subtitle.Text(), "Primary Residence") || strings.Contains(subtitle.Text(), "Address") {
			addressDiv := subtitle.Next().Filter("div.location").First()
			if addressDiv.Length() > 0 {
				addressLink := addressDiv.Find("a.web").First()
				if addressLink.Length() > 0 {
					var addressParts []string
					street := addressLink.Find("span.street").First()
					if street.Length() > 0 {
						addressParts = append(addressParts, strings.TrimSpace(street.Text()))
					}
					city := addressLink.Find("span.city").First()
					if city.Length() > 0 {
						addressParts = append(addressParts, strings.TrimSpace(city.Text()))
					}
					state := addressLink.Find("span.state").First()
					if state.Length() > 0 {
						addressParts = append(addressParts, strings.TrimSpace(state.Text()))
					}
					zipcode := addressLink.Find("span.zip").First()
					if zipcode.Length() > 0 {
						addressParts = append(addressParts, strings.TrimSpace(zipcode.Text()))
					}
					if len(addressParts) > 0 {
						address = strings.Join(addressParts, " ")
					}
				}
			}
		}
	})
	if address == "" {
		residesDiv := record.Find("div.resides").First()
		if residesDiv.Length() > 0 {
			address = strings.TrimSpace(residesDiv.Text())
			address = strings.TrimPrefix(address, "Lives in ")
		}
	}
	if address == "" {
		record.Find("div.location, div.address").Each(func(i int, locationDiv *goquery.Selection) {
			if locationDiv.Text() != "" {
				address = strings.TrimSpace(locationDiv.Text())
				return
			}
		})
	}
	if address != "" {
		person.address = address
	}
	var phones []string
	record.Find("div.subtitle").Each(func(i int, subtitle *goquery.Selection) {
		if strings.Contains(subtitle.Text(), "Phone Numbers") || strings.Contains(subtitle.Text(), "Phone") {
			phoneList := subtitle.Next().Filter("ul.dotted").First()
			if phoneList.Length() > 0 {
				phoneList.Find("a.web").Each(func(j int, phoneLink *goquery.Selection) {
					phone := strings.TrimSpace(phoneLink.Text())
					if phone != "" {
						phones = append(phones, phone)
					}
				})
			}
		}
	})
	if len(phones) == 0 {
		record.Find("*").Each(func(i int, s *goquery.Selection) {
			text := strings.TrimSpace(s.Text())
			phoneRegex := regexp.MustCompile(`\(?(\d{3})\)?[-.\s]?(\d{3})[-.\s]?(\d{4})`)
			if phoneRegex.MatchString(text) && len(text) < 20 {
				phones = append(phones, text)
			}
		})
	}
	if len(phones) > 0 {
		person.phone = strings.Join(phones, ", ")
	}
	scores := make(map[string]string)
	record.Find("div.subtitle").Each(func(i int, subtitle *goquery.Selection) {
		if strings.Contains(subtitle.Text(), "Scores") {
			scoreItems := subtitle.Next().Filter("dl.subfields").First()
			if scoreItems.Length() > 0 {
				scoreItems.Find("div.subfield").Each(func(j int, scoreItem *goquery.Selection) {
					scoreName := scoreItem.Find("dt").First()
					if scoreName.Length() > 0 {
						scoreText := strings.TrimSpace(scoreName.Text())
						re := regexp.MustCompile(`(.*?)\s*\((\d+)\)`)
						if matches := re.FindStringSubmatch(scoreText); len(matches) >= 3 {
							scoreType := strings.TrimSpace(matches[1])
							scoreValue := matches[2]
							scores[strings.ToLower(scoreType)] = scoreValue
						}
					}
				})
			}
		}
	})
	if wealth, ok := scores["wealth"]; ok {
		person.wealth = wealth
	}
	if person.name != "" || person.address != "" || person.phone != "" || person.wealth != "" || person.age != "" {
		return person
	}
	return nil
}

func solveCaptcha() string {
	apiKey := "CAP-B3A08BF66090950B7BF4BBF9DDD2EEF2C58F2C61C2AD66E4B24034C90D20EF63"
	taskReq := map[string]interface{}{
		"clientKey": apiKey,
		"task": map[string]string{
			"type":       "ReCaptchaV2TaskProxyLess",
			"websiteURL": "https://thatsthem.com",
			"websiteKey": "6Ldx4RATAAAAAKBXQ1OdnsaVrzgfTfatWNH84j-n",
		},
	}
	taskJSON, _ := json.Marshal(taskReq)
	resp, err := http.Post("https://api.capsolver.com/createTask", "application/json", strings.NewReader(string(taskJSON)))
	if err != nil {
		return ""
	}
	defer resp.Body.Close()
	var taskResp map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&taskResp)
	if errorID, ok := taskResp["errorId"].(float64); ok && errorID > 0 {
		return ""
	}
	taskID, ok := taskResp["taskId"].(string)
	if !ok {
		return ""
	}
	for tries := 0; tries < 30; tries++ {
		resultReq := map[string]string{"clientKey": apiKey, "taskId": taskID}
		resultJSON, _ := json.Marshal(resultReq)
		resultResp, err := http.Post("https://api.capsolver.com/getTaskResult", "application/json", strings.NewReader(string(resultJSON)))
		if err != nil {
			continue
		}
		var result map[string]interface{}
		json.NewDecoder(resultResp.Body).Decode(&result)
		resultResp.Body.Close()
		if status, ok := result["status"].(string); ok && status == "ready" {
			if solution, ok := result["solution"].(map[string]interface{}); ok {
				if token, ok := solution["gRecaptchaResponse"].(string); ok {
					return token
				}
			}
		}
		time.Sleep(time.Second)
	}
	return ""
}

var csvMux sync.Mutex

func ensureCSV() {
	csvMux.Lock()
	defer csvMux.Unlock()
	csvFile := "output.csv"
	fieldnames := []string{"email", "name", "years", "address", "phones", "wealth"}
	if _, err := os.Stat(csvFile); os.IsNotExist(err) {
		file, err := os.Create(csvFile)
		if err != nil {
			return
		}
		defer file.Close()
		writer := csv.NewWriter(file)
		writer.Write(fieldnames)
		writer.Flush()
	} else {
		file, err := os.Open(csvFile)
		if err != nil {
			return
		}
		defer file.Close()
		reader := csv.NewReader(file)
		records, err := reader.ReadAll()
		needsHeader := false
		if err != nil || len(records) == 0 {
			needsHeader = true
		} else {
			firstLine := records[0]
			if len(firstLine) != len(fieldnames) {
				needsHeader = true
			} else {
				for i, field := range fieldnames {
					if i >= len(firstLine) || firstLine[i] != field {
						needsHeader = true
						break
					}
				}
			}
		}
		if needsHeader {
			file.Close()
			appendFile, err := os.OpenFile(csvFile, os.O_APPEND|os.O_WRONLY, 0644)
			if err != nil {
				return
			}
			defer appendFile.Close()
			writer := csv.NewWriter(appendFile)
			writer.Write(fieldnames)
			writer.Flush()
		}
	}
}

func exportToCSV(person *personInfo, email string) bool {
	if person == nil {
		return false
	}
	csvMux.Lock()
	defer csvMux.Unlock()
	file, err := os.OpenFile("output.csv", os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		return false
	}
	defer file.Close()
	writer := csv.NewWriter(file)
	writer.Write([]string{
		email,
		person.name,
		person.age,
		person.address,
		person.phone,
		person.wealth,
	})
	writer.Flush()
	return true
}

func logHit(email, name string, stats *stats) {
	hitSty := lipgloss.NewStyle().Foreground(lipgloss.Color("#ff00ff")).Bold(true)
	emailSty := lipgloss.NewStyle().Foreground(lipgloss.Color("#666666"))
	var hitMsg string
	if name != "" {
		hitMsg = fmt.Sprintf("%s %s %s",
			hitSty.Render("✓"),
			name,
			emailSty.Render("["+email+"]"))
	} else {
		hitMsg = fmt.Sprintf("%s %s",
			hitSty.Render("✓"),
			emailSty.Render("["+email+"]"))
	}
	select {
	case stats.hitsChan <- hitMsg:
	default:
	}
}
